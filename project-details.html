<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Details - GreenSoft BD</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0f172a',
                        secondary: '#1e293b',
                        accent: '#3b82f6',
                        success: '#10b981',
                        light: '#f8fafc'
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-light text-gray-800">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="text-2xl font-bold text-primary">
                        <i class="fas fa-code text-accent mr-2"></i>GreenSoft BD
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="index.html" class="text-gray-600 hover:text-accent px-3 py-2 rounded-md text-sm font-medium transition-colors">Home</a>
                        <a href="about.html" class="text-gray-600 hover:text-accent px-3 py-2 rounded-md text-sm font-medium transition-colors">About</a>
                        <a href="services.html" class="text-gray-600 hover:text-accent px-3 py-2 rounded-md text-sm font-medium transition-colors">Services</a>
                        <a href="projects.html" class="text-primary hover:text-accent px-3 py-2 rounded-md text-sm font-medium transition-colors">Projects</a>
                        <a href="partners.html" class="text-gray-600 hover:text-accent px-3 py-2 rounded-md text-sm font-medium transition-colors">Partners</a>
                        <a href="contact.html" class="bg-accent text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">Contact</a>
                    </div>
                </div>
                
                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-gray-600 hover:text-accent focus:outline-none focus:text-accent">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
                <a href="index.html" class="text-gray-600 block px-3 py-2 rounded-md text-base font-medium">Home</a>
                <a href="about.html" class="text-gray-600 hover:text-accent block px-3 py-2 rounded-md text-base font-medium">About</a>
                <a href="services.html" class="text-gray-600 hover:text-accent block px-3 py-2 rounded-md text-base font-medium">Services</a>
                <a href="projects.html" class="text-primary block px-3 py-2 rounded-md text-base font-medium">Projects</a>
                <a href="partners.html" class="text-gray-600 hover:text-accent block px-3 py-2 rounded-md text-base font-medium">Partners</a>
                <a href="contact.html" class="bg-accent text-white block px-3 py-2 rounded-md text-base font-medium">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <section class="pt-20 pb-8 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-4">
                    <li>
                        <a href="index.html" class="text-gray-500 hover:text-accent transition-colors">Home</a>
                    </li>
                    <li>
                        <i class="fas fa-chevron-right text-gray-400 text-sm"></i>
                    </li>
                    <li>
                        <a href="projects.html" class="text-gray-500 hover:text-accent transition-colors">Projects</a>
                    </li>
                    <li>
                        <i class="fas fa-chevron-right text-gray-400 text-sm"></i>
                    </li>
                    <li>
                        <span class="text-primary font-medium" id="project-breadcrumb">Project Details</span>
                    </li>
                </ol>
            </nav>
        </div>
    </section>

    <!-- Project Header -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <div class="flex items-center mb-4">
                        <span id="project-category" class="bg-accent/10 text-accent px-3 py-1 rounded-full text-sm font-medium mr-4">SaaS</span>
                        <span class="text-gray-500">Completed in 2024</span>
                    </div>
                    <h1 id="project-title" class="text-4xl md:text-5xl font-bold text-primary mb-6">Enatu E-commerce Platform</h1>
                    <p id="project-description" class="text-xl text-gray-600 mb-8">
                        A comprehensive e-commerce solution built for Enatu, featuring advanced inventory management, real-time analytics, multi-vendor support, and seamless payment integration.
                    </p>
                    <div class="flex flex-wrap gap-4 mb-8">
                        <a href="#" class="bg-accent hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center">
                            <i class="fas fa-external-link-alt mr-2"></i>
                            Live Demo
                        </a>
                        <a href="#" class="border-2 border-accent text-accent hover:bg-accent hover:text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center">
                            <i class="fab fa-github mr-2"></i>
                            View Code
                        </a>
                    </div>
                </div>
                <div class="bg-gradient-to-br from-accent to-blue-600 rounded-2xl p-8 text-white">
                    <div class="text-center">
                        <i id="project-icon" class="fas fa-shopping-cart text-6xl mb-6"></i>
                        <h3 class="text-2xl font-bold mb-4">Project Highlights</h3>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div class="bg-white/20 rounded-lg p-3">
                                <div class="font-bold text-lg">6 months</div>
                                <div>Development Time</div>
                            </div>
                            <div class="bg-white/20 rounded-lg p-3">
                                <div class="font-bold text-lg">5 developers</div>
                                <div>Team Size</div>
                            </div>
                            <div class="bg-white/20 rounded-lg p-3">
                                <div class="font-bold text-lg">10k+</div>
                                <div>Daily Users</div>
                            </div>
                            <div class="bg-white/20 rounded-lg p-3">
                                <div class="font-bold text-lg">99.9%</div>
                                <div>Uptime</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Project Screenshots -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-primary mb-4">Project Screenshots</h2>
                <p class="text-xl text-gray-600">Visual showcase of the application interface and features</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-shadow">
                    <div class="bg-gradient-to-br from-accent/20 to-blue-600/20 h-48 flex items-center justify-center">
                        <i class="fas fa-image text-4xl text-gray-400"></i>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold mb-2">Dashboard Overview</h3>
                        <p class="text-gray-600 text-sm">Main dashboard with key metrics and analytics</p>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-shadow">
                    <div class="bg-gradient-to-br from-success/20 to-green-600/20 h-48 flex items-center justify-center">
                        <i class="fas fa-image text-4xl text-gray-400"></i>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold mb-2">Product Management</h3>
                        <p class="text-gray-600 text-sm">Inventory and product catalog management interface</p>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-shadow">
                    <div class="bg-gradient-to-br from-purple-500/20 to-purple-700/20 h-48 flex items-center justify-center">
                        <i class="fas fa-image text-4xl text-gray-400"></i>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold mb-2">Analytics Dashboard</h3>
                        <p class="text-gray-600 text-sm">Real-time sales and performance analytics</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tech Stack & Features -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Tech Stack -->
                <div>
                    <h2 class="text-3xl font-bold text-primary mb-8">Technology Stack</h2>
                    <div class="space-y-6">
                        <div>
                            <h3 class="text-lg font-semibold mb-3">Frontend</h3>
                            <div class="flex flex-wrap gap-3" id="frontend-tech">
                                <span class="bg-accent/10 text-accent px-3 py-2 rounded-lg flex items-center">
                                    <i class="fab fa-react mr-2"></i>React.js
                                </span>
                                <span class="bg-success/10 text-success px-3 py-2 rounded-lg flex items-center">
                                    <i class="fab fa-js-square mr-2"></i>TypeScript
                                </span>
                                <span class="bg-purple-500/10 text-purple-500 px-3 py-2 rounded-lg flex items-center">
                                    <i class="fas fa-paint-brush mr-2"></i>Tailwind CSS
                                </span>
                            </div>
                        </div>
                        
                        <div>
                            <h3 class="text-lg font-semibold mb-3">Backend</h3>
                            <div class="flex flex-wrap gap-3" id="backend-tech">
                                <span class="bg-success/10 text-success px-3 py-2 rounded-lg flex items-center">
                                    <i class="fab fa-node-js mr-2"></i>Node.js
                                </span>
                                <span class="bg-accent/10 text-accent px-3 py-2 rounded-lg flex items-center">
                                    <i class="fas fa-server mr-2"></i>Express.js
                                </span>
                                <span class="bg-orange-500/10 text-orange-500 px-3 py-2 rounded-lg flex items-center">
                                    <i class="fas fa-shield-alt mr-2"></i>JWT Auth
                                </span>
                            </div>
                        </div>
                        
                        <div>
                            <h3 class="text-lg font-semibold mb-3">Database & Cloud</h3>
                            <div class="flex flex-wrap gap-3" id="database-tech">
                                <span class="bg-success/10 text-success px-3 py-2 rounded-lg flex items-center">
                                    <i class="fas fa-database mr-2"></i>MongoDB
                                </span>
                                <span class="bg-orange-500/10 text-orange-500 px-3 py-2 rounded-lg flex items-center">
                                    <i class="fab fa-aws mr-2"></i>AWS
                                </span>
                                <span class="bg-purple-500/10 text-purple-500 px-3 py-2 rounded-lg flex items-center">
                                    <i class="fas fa-memory mr-2"></i>Redis
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Key Features -->
                <div>
                    <h2 class="text-3xl font-bold text-primary mb-8">Key Features</h2>
                    <div class="space-y-4" id="project-features">
                        <div class="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                            <div class="bg-accent/20 w-10 h-10 rounded-lg flex items-center justify-center">
                                <i class="fas fa-shopping-cart text-accent"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold mb-1">Advanced E-commerce Engine</h3>
                                <p class="text-gray-600 text-sm">Complete shopping cart, checkout, and order management system</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                            <div class="bg-success/20 w-10 h-10 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-line text-success"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold mb-1">Real-time Analytics</h3>
                                <p class="text-gray-600 text-sm">Live sales tracking, inventory monitoring, and performance metrics</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                            <div class="bg-purple-500/20 w-10 h-10 rounded-lg flex items-center justify-center">
                                <i class="fas fa-users text-purple-500"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold mb-1">Multi-vendor Support</h3>
                                <p class="text-gray-600 text-sm">Support for multiple vendors with individual dashboards and commission tracking</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                            <div class="bg-orange-500/20 w-10 h-10 rounded-lg flex items-center justify-center">
                                <i class="fas fa-mobile-alt text-orange-500"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold mb-1">Mobile Responsive</h3>
                                <p class="text-gray-600 text-sm">Fully responsive design optimized for all devices and screen sizes</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Client Information -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white rounded-2xl p-8 md:p-12 shadow-lg">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div>
                        <h2 class="text-3xl font-bold text-primary mb-6">Client Information</h2>
                        <div class="space-y-4">
                            <div>
                                <h3 class="font-semibold text-lg mb-2" id="client-name">Enatu</h3>
                                <p class="text-gray-600" id="client-description">
                                    A growing e-commerce company specializing in consumer electronics and digital products. They needed a scalable platform to handle their expanding product catalog and increasing customer base.
                                </p>
                            </div>
                            <div class="grid grid-cols-2 gap-4 pt-4">
                                <div>
                                    <h4 class="font-semibold text-gray-800">Industry</h4>
                                    <p class="text-gray-600" id="client-industry">E-commerce</p>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Company Size</h4>
                                    <p class="text-gray-600" id="client-size">50-100 employees</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-br from-accent/10 to-success/10 rounded-xl p-8">
                        <h3 class="text-xl font-semibold mb-6">Project Results</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Sales Increase</span>
                                <span class="font-bold text-success">+150%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Page Load Speed</span>
                                <span class="font-bold text-accent">2.1s</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">User Satisfaction</span>
                                <span class="font-bold text-purple-500">98%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Mobile Traffic</span>
                                <span class="font-bold text-orange-500">+200%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Projects -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-primary mb-4">Related Projects</h2>
                <p class="text-xl text-gray-600">Other projects you might find interesting</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-shadow group">
                    <div class="bg-gradient-to-br from-success to-green-600 h-48 flex items-center justify-center">
                        <i class="fab fa-shopify text-white text-4xl group-hover:scale-110 transition-transform"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2">Inventory Manager App</h3>
                        <p class="text-gray-600 mb-4">Advanced Shopify app for inventory tracking and management.</p>
                        <a href="project-details.html?id=inventory" class="text-accent font-semibold hover:underline">View Details →</a>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-shadow group">
                    <div class="bg-gradient-to-br from-purple-500 to-purple-700 h-48 flex items-center justify-center">
                        <i class="fas fa-chart-line text-white text-4xl group-hover:scale-110 transition-transform"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2">Analytics Dashboard</h3>
                        <p class="text-gray-600 mb-4">Real-time business intelligence and reporting platform.</p>
                        <a href="project-details.html?id=analytics" class="text-accent font-semibold hover:underline">View Details →</a>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-shadow group">
                    <div class="bg-gradient-to-br from-teal-500 to-cyan-500 h-48 flex items-center justify-center">
                        <i class="fas fa-tasks text-white text-4xl group-hover:scale-110 transition-transform"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2">TaskFlow Pro</h3>
                        <p class="text-gray-600 mb-4">Collaborative task management and project tracking platform.</p>
                        <a href="project-details.html?id=taskflow" class="text-accent font-semibold hover:underline">View Details →</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-br from-primary to-secondary text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Interested in a Similar Project?</h2>
            <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Let's discuss how we can create a custom solution tailored to your specific business needs.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="contact.html" class="bg-accent hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                    Start Your Project
                </a>
                <a href="projects.html" class="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-3 rounded-lg font-semibold transition-colors">
                    View All Projects
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-primary text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-code text-accent text-2xl mr-2"></i>
                        <span class="text-2xl font-bold">GreenSoft BD</span>
                    </div>
                    <p class="text-gray-300 mb-6 max-w-md">
                        Building innovative software solutions that drive business growth and digital transformation.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-300 hover:text-accent transition-colors">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-accent transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-accent transition-colors">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-accent transition-colors">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="about.html" class="text-gray-300 hover:text-accent transition-colors">About Us</a></li>
                        <li><a href="services.html" class="text-gray-300 hover:text-accent transition-colors">Services</a></li>
                        <li><a href="projects.html" class="text-gray-300 hover:text-accent transition-colors">Projects</a></li>
                        <li><a href="partners.html" class="text-gray-300 hover:text-accent transition-colors">Partners</a></li>
                        <li><a href="contact.html" class="text-gray-300 hover:text-accent transition-colors">Contact</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Info</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-2"></i>
                            <EMAIL>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone mr-2"></i>
                            +880 1234 567890
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            Dhaka, Bangladesh
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-12 pt-8 text-center text-gray-300">
                <p>&copy; 2024 GreenSoft BD. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script>
        // Mobile Menu Toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Project Data
        const projects = {
            enatu: {
                title: "Enatu E-commerce Platform",
                category: "SaaS",
                icon: "fas fa-shopping-cart",
                description: "A comprehensive e-commerce solution built for Enatu, featuring advanced inventory management, real-time analytics, multi-vendor support, and seamless payment integration.",
                client: {
                    name: "Enatu",
                    description: "A growing e-commerce company specializing in consumer electronics and digital products. They needed a scalable platform to handle their expanding product catalog and increasing customer base.",
                    industry: "E-commerce",
                    size: "50-100 employees"
                }
            },
            inventory: {
                title: "Inventory Manager App",
                category: "Shopify",
                icon: "fab fa-shopify",
                description: "Advanced Shopify app for inventory tracking, automated reordering, and comprehensive stock analytics with real-time synchronization.",
                client: {
                    name: "Multiple Shopify Stores",
                    description: "Various Shopify store owners who needed better inventory management and automated reordering capabilities.",
                    industry: "E-commerce",
                    size: "Various"
                }
            },
            analytics: {
                title: "Business Analytics Dashboard",
                category: "SaaS",
                icon: "fas fa-chart-line",
                description: "Real-time business intelligence platform with custom reporting, data visualization, and predictive analytics for enterprise clients.",
                client: {
                    name: "TechCorp Solutions",
                    description: "A technology consulting firm that needed comprehensive analytics to track their business performance and client metrics.",
                    industry: "Technology Consulting",
                    size: "100-200 employees"
                }
            }
        };

        // Get project ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const projectId = urlParams.get('id') || 'enatu';
        const project = projects[projectId] || projects.enatu;

        // Update page content
        document.getElementById('project-breadcrumb').textContent = project.title;
        document.getElementById('project-category').textContent = project.category;
        document.getElementById('project-title').textContent = project.title;
        document.getElementById('project-description').textContent = project.description;
        document.getElementById('project-icon').className = project.icon + ' text-6xl mb-6';
        document.getElementById('client-name').textContent = project.client.name;
        document.getElementById('client-description').textContent = project.client.description;
        document.getElementById('client-industry').textContent = project.client.industry;
        document.getElementById('client-size').textContent = project.client.size;
        document.title = project.title + ' - GreenSoft BD';
    </script>
</body>
</html>
