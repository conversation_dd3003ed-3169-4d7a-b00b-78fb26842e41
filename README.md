# GreenSoft BD - Software Company Website

A modern, clean, and fully responsive software company website built with HTML and TailwindCSS.

## 🌟 Features

### Pages
- **Home** - Hero section, about preview, services highlight, partner logos, featured projects
- **About** - Company mission, team, expertise areas
- **Services** - Detailed services (SaaS, Shopify Apps, Custom Software)
- **Projects** - Project grid with filtering and hover effects
- **Project Details** - Individual project pages with tech stack and client info
- **Partners** - Partner showcase and case studies
- **Contact** - Contact form, business info, and FAQ

### Design Features
- **Mobile-First Design** - Fully responsive across all devices
- **Modern UI** - Clean, minimal design with good spacing
- **Professional Color Palette** - Tech-oriented color scheme
- **Interactive Elements** - Hover effects, transitions, and animations
- **Consistent Navigation** - Fixed header with mobile hamburger menu
- **TailwindCSS Styling** - No external CSS files needed

### Technical Features
- **Pure HTML/CSS** - No framework dependencies except TailwindCSS
- **Font Awesome Icons** - Professional iconography
- **JavaScript Interactions** - Mobile menu, project filtering, form handling
- **SEO Friendly** - Proper meta tags and semantic HTML
- **Fast Loading** - Optimized for performance

## 🎨 Color Palette

```css
primary: '#0f172a'    /* Dark slate */
secondary: '#1e293b'  /* Slate */
accent: '#3b82f6'     /* Blue */
success: '#10b981'    /* Emerald */
light: '#f8fafc'      /* Slate 50 */
```

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 🚀 Getting Started

1. Clone or download the files
2. Open `index.html` in your browser
3. No build process required - everything works out of the box!

## 📁 File Structure

```
├── index.html          # Home page
├── about.html          # About page
├── services.html       # Services page
├── projects.html       # Projects listing
├── project-details.html # Project detail template
├── partners.html       # Partners page
├── contact.html        # Contact page
└── README.md          # This file
```

## 🔧 Customization

### Colors
Update the Tailwind config in each HTML file:
```javascript
tailwind.config = {
    theme: {
        extend: {
            colors: {
                primary: '#your-color',
                secondary: '#your-color',
                accent: '#your-color',
                success: '#your-color',
                light: '#your-color'
            }
        }
    }
}
```

### Content
- Update company information in all pages
- Replace placeholder project data
- Modify contact information
- Add real partner logos and testimonials

### Styling
- All styling is done with TailwindCSS classes
- No external CSS files to modify
- Easy to customize with Tailwind utilities

## 📋 Features by Page

### Home Page
- Hero section with call-to-action buttons
- About preview with key benefits
- Services highlight cards
- Featured projects grid
- Partner logos section
- Responsive navigation and footer

### About Page
- Company mission and values
- Team member profiles
- Technology expertise grid
- Statistics and achievements

### Services Page
- Detailed service descriptions
- Technology stacks for each service
- Process workflows
- Call-to-action sections

### Projects Page
- Filterable project grid
- Hover effects and animations
- Project categories (SaaS, Shopify, Custom)
- Statistics section

### Project Details Page
- Dynamic content loading via URL parameters
- Technology stack display
- Client information
- Project screenshots placeholder
- Related projects

### Partners Page
- Partner categories
- Featured partnerships
- Success stories and testimonials
- Partnership benefits

### Contact Page
- Comprehensive contact form
- Business information
- Interactive elements
- FAQ section
- Map placeholder

## 🌐 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers

## 📞 Contact Information

- **Email**: <EMAIL>
- **Phone**: +880 1234 567890
- **Location**: Dhaka, Bangladesh

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

1. Fork the project
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Open a pull request

---

Built with ❤️ using HTML, TailwindCSS, and JavaScript
