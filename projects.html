<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Projects - GreenSoft BD</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0f172a',
                        secondary: '#1e293b',
                        accent: '#3b82f6',
                        success: '#10b981',
                        light: '#f8fafc'
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-light text-gray-800">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="text-2xl font-bold text-primary">
                        <i class="fas fa-code text-accent mr-2"></i>GreenSoft BD
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="index.html" class="text-gray-600 hover:text-accent px-3 py-2 rounded-md text-sm font-medium transition-colors">Home</a>
                        <a href="about.html" class="text-gray-600 hover:text-accent px-3 py-2 rounded-md text-sm font-medium transition-colors">About</a>
                        <a href="services.html" class="text-gray-600 hover:text-accent px-3 py-2 rounded-md text-sm font-medium transition-colors">Services</a>
                        <a href="projects.html" class="text-primary hover:text-accent px-3 py-2 rounded-md text-sm font-medium transition-colors">Projects</a>
                        <a href="partners.html" class="text-gray-600 hover:text-accent px-3 py-2 rounded-md text-sm font-medium transition-colors">Partners</a>
                        <a href="contact.html" class="bg-accent text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">Contact</a>
                    </div>
                </div>
                
                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-gray-600 hover:text-accent focus:outline-none focus:text-accent">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
                <a href="index.html" class="text-gray-600 block px-3 py-2 rounded-md text-base font-medium">Home</a>
                <a href="about.html" class="text-gray-600 hover:text-accent block px-3 py-2 rounded-md text-base font-medium">About</a>
                <a href="services.html" class="text-gray-600 hover:text-accent block px-3 py-2 rounded-md text-base font-medium">Services</a>
                <a href="projects.html" class="text-primary block px-3 py-2 rounded-md text-base font-medium">Projects</a>
                <a href="partners.html" class="text-gray-600 hover:text-accent block px-3 py-2 rounded-md text-base font-medium">Partners</a>
                <a href="contact.html" class="bg-accent text-white block px-3 py-2 rounded-md text-base font-medium">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-16 bg-gradient-to-br from-primary to-secondary text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">Our Projects</h1>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Explore our portfolio of successful software solutions. From SaaS platforms to Shopify apps, see how we've helped businesses transform digitally.
                </p>
            </div>
        </div>
    </section>

    <!-- Filter Section -->
    <section class="py-12 bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-wrap justify-center gap-4">
                <button class="filter-btn active bg-accent text-white px-6 py-2 rounded-full font-medium transition-colors" data-filter="all">
                    All Projects
                </button>
                <button class="filter-btn bg-gray-200 text-gray-700 hover:bg-accent hover:text-white px-6 py-2 rounded-full font-medium transition-colors" data-filter="saas">
                    SaaS
                </button>
                <button class="filter-btn bg-gray-200 text-gray-700 hover:bg-accent hover:text-white px-6 py-2 rounded-full font-medium transition-colors" data-filter="shopify">
                    Shopify Apps
                </button>
                <button class="filter-btn bg-gray-200 text-gray-700 hover:bg-accent hover:text-white px-6 py-2 rounded-full font-medium transition-colors" data-filter="custom">
                    Custom Software
                </button>
            </div>
        </div>
    </section>

    <!-- Projects Grid -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="projects-grid">
                
                <!-- Project 1: Enatu E-commerce -->
                <div class="project-card bg-white rounded-xl overflow-hidden shadow-md hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 group" data-category="saas custom">
                    <div class="relative bg-gradient-to-br from-accent to-blue-600 h-48 overflow-hidden">
                        <div class="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors"></div>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <i class="fas fa-shopping-cart text-white text-4xl group-hover:scale-110 transition-transform"></i>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="bg-white/20 backdrop-blur-sm text-white px-2 py-1 rounded text-sm">SaaS</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 group-hover:text-accent transition-colors">Enatu E-commerce Platform</h3>
                        <p class="text-gray-600 mb-4">Complete e-commerce solution with advanced inventory management, real-time analytics, and multi-vendor support.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-accent/10 text-accent px-2 py-1 rounded text-sm">React</span>
                            <span class="bg-success/10 text-success px-2 py-1 rounded text-sm">Node.js</span>
                            <span class="bg-purple-500/10 text-purple-500 px-2 py-1 rounded text-sm">MongoDB</span>
                            <span class="bg-orange-500/10 text-orange-500 px-2 py-1 rounded text-sm">AWS</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <a href="project-details.html?id=enatu" class="text-accent font-semibold hover:underline">View Details →</a>
                            <div class="flex space-x-2">
                                <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                                    <i class="fab fa-github"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Project 2: Inventory Manager App -->
                <div class="project-card bg-white rounded-xl overflow-hidden shadow-md hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 group" data-category="shopify">
                    <div class="relative bg-gradient-to-br from-success to-green-600 h-48 overflow-hidden">
                        <div class="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors"></div>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <i class="fab fa-shopify text-white text-4xl group-hover:scale-110 transition-transform"></i>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="bg-white/20 backdrop-blur-sm text-white px-2 py-1 rounded text-sm">Shopify</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 group-hover:text-accent transition-colors">Inventory Manager App</h3>
                        <p class="text-gray-600 mb-4">Advanced Shopify app for inventory tracking, automated reordering, and comprehensive stock analytics.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-success/10 text-success px-2 py-1 rounded text-sm">Shopify API</span>
                            <span class="bg-accent/10 text-accent px-2 py-1 rounded text-sm">Express</span>
                            <span class="bg-orange-500/10 text-orange-500 px-2 py-1 rounded text-sm">PostgreSQL</span>
                            <span class="bg-purple-500/10 text-purple-500 px-2 py-1 rounded text-sm">React</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <a href="project-details.html?id=inventory" class="text-accent font-semibold hover:underline">View Details →</a>
                            <div class="flex space-x-2">
                                <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                                    <i class="fab fa-github"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Project 3: Analytics Dashboard -->
                <div class="project-card bg-white rounded-xl overflow-hidden shadow-md hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 group" data-category="saas custom">
                    <div class="relative bg-gradient-to-br from-purple-500 to-purple-700 h-48 overflow-hidden">
                        <div class="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors"></div>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <i class="fas fa-chart-line text-white text-4xl group-hover:scale-110 transition-transform"></i>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="bg-white/20 backdrop-blur-sm text-white px-2 py-1 rounded text-sm">SaaS</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 group-hover:text-accent transition-colors">Business Analytics Dashboard</h3>
                        <p class="text-gray-600 mb-4">Real-time business intelligence platform with custom reporting, data visualization, and predictive analytics.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-accent/10 text-accent px-2 py-1 rounded text-sm">Vue.js</span>
                            <span class="bg-success/10 text-success px-2 py-1 rounded text-sm">Python</span>
                            <span class="bg-purple-500/10 text-purple-500 px-2 py-1 rounded text-sm">Redis</span>
                            <span class="bg-orange-500/10 text-orange-500 px-2 py-1 rounded text-sm">D3.js</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <a href="project-details.html?id=analytics" class="text-accent font-semibold hover:underline">View Details →</a>
                            <div class="flex space-x-2">
                                <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                                    <i class="fab fa-github"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Project 4: CRM System -->
                <div class="project-card bg-white rounded-xl overflow-hidden shadow-md hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 group" data-category="custom">
                    <div class="relative bg-gradient-to-br from-orange-500 to-red-500 h-48 overflow-hidden">
                        <div class="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors"></div>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <i class="fas fa-users text-white text-4xl group-hover:scale-110 transition-transform"></i>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="bg-white/20 backdrop-blur-sm text-white px-2 py-1 rounded text-sm">Custom</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 group-hover:text-accent transition-colors">Enterprise CRM System</h3>
                        <p class="text-gray-600 mb-4">Comprehensive customer relationship management system with lead tracking, sales pipeline, and automation features.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-accent/10 text-accent px-2 py-1 rounded text-sm">Angular</span>
                            <span class="bg-success/10 text-success px-2 py-1 rounded text-sm">.NET Core</span>
                            <span class="bg-purple-500/10 text-purple-500 px-2 py-1 rounded text-sm">SQL Server</span>
                            <span class="bg-orange-500/10 text-orange-500 px-2 py-1 rounded text-sm">Azure</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <a href="project-details.html?id=crm" class="text-accent font-semibold hover:underline">View Details →</a>
                            <div class="flex space-x-2">
                                <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                                    <i class="fab fa-github"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Project 5: Product Recommendation Engine -->
                <div class="project-card bg-white rounded-xl overflow-hidden shadow-md hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 group" data-category="shopify">
                    <div class="relative bg-gradient-to-br from-pink-500 to-rose-500 h-48 overflow-hidden">
                        <div class="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors"></div>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <i class="fas fa-brain text-white text-4xl group-hover:scale-110 transition-transform"></i>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="bg-white/20 backdrop-blur-sm text-white px-2 py-1 rounded text-sm">Shopify</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 group-hover:text-accent transition-colors">AI Product Recommendations</h3>
                        <p class="text-gray-600 mb-4">Machine learning-powered Shopify app that provides personalized product recommendations to increase sales.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-accent/10 text-accent px-2 py-1 rounded text-sm">Python</span>
                            <span class="bg-success/10 text-success px-2 py-1 rounded text-sm">TensorFlow</span>
                            <span class="bg-purple-500/10 text-purple-500 px-2 py-1 rounded text-sm">Shopify API</span>
                            <span class="bg-orange-500/10 text-orange-500 px-2 py-1 rounded text-sm">FastAPI</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <a href="project-details.html?id=recommendations" class="text-accent font-semibold hover:underline">View Details →</a>
                            <div class="flex space-x-2">
                                <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                                    <i class="fab fa-github"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Project 6: Task Management SaaS -->
                <div class="project-card bg-white rounded-xl overflow-hidden shadow-md hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 group" data-category="saas">
                    <div class="relative bg-gradient-to-br from-teal-500 to-cyan-500 h-48 overflow-hidden">
                        <div class="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors"></div>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <i class="fas fa-tasks text-white text-4xl group-hover:scale-110 transition-transform"></i>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="bg-white/20 backdrop-blur-sm text-white px-2 py-1 rounded text-sm">SaaS</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 group-hover:text-accent transition-colors">TaskFlow Pro</h3>
                        <p class="text-gray-600 mb-4">Collaborative task management platform with team collaboration, time tracking, and project analytics features.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-accent/10 text-accent px-2 py-1 rounded text-sm">React</span>
                            <span class="bg-success/10 text-success px-2 py-1 rounded text-sm">Node.js</span>
                            <span class="bg-purple-500/10 text-purple-500 px-2 py-1 rounded text-sm">Socket.io</span>
                            <span class="bg-orange-500/10 text-orange-500 px-2 py-1 rounded text-sm">MongoDB</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <a href="project-details.html?id=taskflow" class="text-accent font-semibold hover:underline">View Details →</a>
                            <div class="flex space-x-2">
                                <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                                    <i class="fab fa-github"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-primary mb-4">Project Statistics</h2>
                <p class="text-xl text-gray-600">Numbers that showcase our commitment to excellence</p>
            </div>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="text-4xl font-bold text-accent mb-2">50+</div>
                    <div class="text-gray-600">Projects Completed</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-success mb-2">98%</div>
                    <div class="text-gray-600">Client Satisfaction</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-purple-500 mb-2">25+</div>
                    <div class="text-gray-600">Happy Clients</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-orange-500 mb-2">5+</div>
                    <div class="text-gray-600">Years Experience</div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-br from-primary to-secondary text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Start Your Project?</h2>
            <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Let's discuss your ideas and create something amazing together.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="contact.html" class="bg-accent hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                    Start Your Project
                </a>
                <a href="services.html" class="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-3 rounded-lg font-semibold transition-colors">
                    View Services
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-primary text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-code text-accent text-2xl mr-2"></i>
                        <span class="text-2xl font-bold">GreenSoft BD</span>
                    </div>
                    <p class="text-gray-300 mb-6 max-w-md">
                        Building innovative software solutions that drive business growth and digital transformation.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-300 hover:text-accent transition-colors">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-accent transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-accent transition-colors">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-accent transition-colors">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="about.html" class="text-gray-300 hover:text-accent transition-colors">About Us</a></li>
                        <li><a href="services.html" class="text-gray-300 hover:text-accent transition-colors">Services</a></li>
                        <li><a href="projects.html" class="text-gray-300 hover:text-accent transition-colors">Projects</a></li>
                        <li><a href="partners.html" class="text-gray-300 hover:text-accent transition-colors">Partners</a></li>
                        <li><a href="contact.html" class="text-gray-300 hover:text-accent transition-colors">Contact</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Info</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-2"></i>
                            <EMAIL>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone mr-2"></i>
                            +880 1234 567890
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            Dhaka, Bangladesh
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-12 pt-8 text-center text-gray-300">
                <p>&copy; 2024 GreenSoft BD. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script>
        // Mobile Menu Toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Project Filter Functionality
        const filterButtons = document.querySelectorAll('.filter-btn');
        const projectCards = document.querySelectorAll('.project-card');

        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons
                filterButtons.forEach(btn => {
                    btn.classList.remove('active', 'bg-accent', 'text-white');
                    btn.classList.add('bg-gray-200', 'text-gray-700');
                });

                // Add active class to clicked button
                button.classList.add('active', 'bg-accent', 'text-white');
                button.classList.remove('bg-gray-200', 'text-gray-700');

                const filter = button.getAttribute('data-filter');

                // Filter projects
                projectCards.forEach(card => {
                    if (filter === 'all' || card.getAttribute('data-category').includes(filter)) {
                        card.style.display = 'block';
                        setTimeout(() => {
                            card.style.opacity = '1';
                            card.style.transform = 'translateY(0)';
                        }, 100);
                    } else {
                        card.style.opacity = '0';
                        card.style.transform = 'translateY(20px)';
                        setTimeout(() => {
                            card.style.display = 'none';
                        }, 300);
                    }
                });
            });
        });
    </script>
</body>
</html>
