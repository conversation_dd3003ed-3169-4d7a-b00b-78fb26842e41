<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - GreenSoft BD</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0f172a',
                        secondary: '#1e293b',
                        accent: '#3b82f6',
                        success: '#10b981',
                        light: '#f8fafc'
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-light text-gray-800">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="text-2xl font-bold text-primary">
                        <i class="fas fa-code text-accent mr-2"></i>GreenSoft BD
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="index.html" class="text-gray-600 hover:text-accent px-3 py-2 rounded-md text-sm font-medium transition-colors">Home</a>
                        <a href="about.html" class="text-gray-600 hover:text-accent px-3 py-2 rounded-md text-sm font-medium transition-colors">About</a>
                        <a href="services.html" class="text-gray-600 hover:text-accent px-3 py-2 rounded-md text-sm font-medium transition-colors">Services</a>
                        <a href="projects.html" class="text-gray-600 hover:text-accent px-3 py-2 rounded-md text-sm font-medium transition-colors">Projects</a>
                        <a href="partners.html" class="text-gray-600 hover:text-accent px-3 py-2 rounded-md text-sm font-medium transition-colors">Partners</a>
                        <a href="contact.html" class="bg-accent text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">Contact</a>
                    </div>
                </div>
                
                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-gray-600 hover:text-accent focus:outline-none focus:text-accent">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
                <a href="index.html" class="text-gray-600 block px-3 py-2 rounded-md text-base font-medium">Home</a>
                <a href="about.html" class="text-gray-600 hover:text-accent block px-3 py-2 rounded-md text-base font-medium">About</a>
                <a href="services.html" class="text-gray-600 hover:text-accent block px-3 py-2 rounded-md text-base font-medium">Services</a>
                <a href="projects.html" class="text-gray-600 hover:text-accent block px-3 py-2 rounded-md text-base font-medium">Projects</a>
                <a href="partners.html" class="text-gray-600 hover:text-accent block px-3 py-2 rounded-md text-base font-medium">Partners</a>
                <a href="contact.html" class="bg-accent text-white block px-3 py-2 rounded-md text-base font-medium">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-16 bg-gradient-to-br from-primary to-secondary text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">Get In Touch</h1>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Ready to start your next project? We'd love to hear from you. Let's discuss how we can help bring your ideas to life.
                </p>
            </div>
        </div>
    </section>

    <!-- Contact Form & Info -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Contact Form -->
                <div>
                    <h2 class="text-3xl font-bold text-primary mb-8">Send us a Message</h2>
                    <form id="contact-form" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                                <input type="text" id="firstName" name="firstName" required 
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent transition-colors">
                            </div>
                            <div>
                                <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                                <input type="text" id="lastName" name="lastName" required 
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent transition-colors">
                            </div>
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                            <input type="email" id="email" name="email" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent transition-colors">
                        </div>
                        
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                            <input type="tel" id="phone" name="phone" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent transition-colors">
                        </div>
                        
                        <div>
                            <label for="company" class="block text-sm font-medium text-gray-700 mb-2">Company</label>
                            <input type="text" id="company" name="company" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent transition-colors">
                        </div>
                        
                        <div>
                            <label for="service" class="block text-sm font-medium text-gray-700 mb-2">Service Interested In</label>
                            <select id="service" name="service" 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent transition-colors">
                                <option value="">Select a service</option>
                                <option value="saas">SaaS Development</option>
                                <option value="shopify">Shopify Apps</option>
                                <option value="custom">Custom Software</option>
                                <option value="mobile">Mobile Apps</option>
                                <option value="consulting">Consulting</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="budget" class="block text-sm font-medium text-gray-700 mb-2">Project Budget</label>
                            <select id="budget" name="budget" 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent transition-colors">
                                <option value="">Select budget range</option>
                                <option value="5k-10k">$5,000 - $10,000</option>
                                <option value="10k-25k">$10,000 - $25,000</option>
                                <option value="25k-50k">$25,000 - $50,000</option>
                                <option value="50k-100k">$50,000 - $100,000</option>
                                <option value="100k+">$100,000+</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Project Details *</label>
                            <textarea id="message" name="message" rows="6" required 
                                      placeholder="Tell us about your project, requirements, timeline, and any specific needs..."
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent transition-colors resize-vertical"></textarea>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="newsletter" name="newsletter" 
                                   class="h-4 w-4 text-accent focus:ring-accent border-gray-300 rounded">
                            <label for="newsletter" class="ml-2 block text-sm text-gray-700">
                                Subscribe to our newsletter for updates and tech insights
                            </label>
                        </div>
                        
                        <button type="submit" 
                                class="w-full bg-accent hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors flex items-center justify-center">
                            <i class="fas fa-paper-plane mr-2"></i>
                            Send Message
                        </button>
                    </form>
                </div>
                
                <!-- Contact Information -->
                <div>
                    <h2 class="text-3xl font-bold text-primary mb-8">Contact Information</h2>
                    
                    <div class="space-y-8">
                        <!-- Office Info -->
                        <div class="bg-gray-50 rounded-xl p-6">
                            <h3 class="text-xl font-semibold mb-4 flex items-center">
                                <i class="fas fa-building text-accent mr-3"></i>
                                Our Office
                            </h3>
                            <div class="space-y-3 text-gray-600">
                                <p class="flex items-center">
                                    <i class="fas fa-map-marker-alt text-accent mr-3 w-5"></i>
                                    123 Tech Street, Gulshan-2, Dhaka 1212, Bangladesh
                                </p>
                                <p class="flex items-center">
                                    <i class="fas fa-phone text-accent mr-3 w-5"></i>
                                    +880 1234 567890
                                </p>
                                <p class="flex items-center">
                                    <i class="fas fa-envelope text-accent mr-3 w-5"></i>
                                    <EMAIL>
                                </p>
                            </div>
                        </div>
                        
                        <!-- Business Hours -->
                        <div class="bg-gray-50 rounded-xl p-6">
                            <h3 class="text-xl font-semibold mb-4 flex items-center">
                                <i class="fas fa-clock text-accent mr-3"></i>
                                Business Hours
                            </h3>
                            <div class="space-y-2 text-gray-600">
                                <div class="flex justify-between">
                                    <span>Monday - Friday</span>
                                    <span>9:00 AM - 6:00 PM</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Saturday</span>
                                    <span>10:00 AM - 4:00 PM</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Sunday</span>
                                    <span>Closed</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Quick Contact -->
                        <div class="bg-gradient-to-br from-accent/10 to-success/10 rounded-xl p-6">
                            <h3 class="text-xl font-semibold mb-4">Quick Contact</h3>
                            <div class="space-y-4">
                                <a href="tel:+8801234567890" 
                                   class="flex items-center p-3 bg-white rounded-lg hover:shadow-md transition-shadow">
                                    <i class="fas fa-phone text-accent mr-3"></i>
                                    <span>Call us directly</span>
                                </a>
                                <a href="mailto:<EMAIL>" 
                                   class="flex items-center p-3 bg-white rounded-lg hover:shadow-md transition-shadow">
                                    <i class="fas fa-envelope text-accent mr-3"></i>
                                    <span>Send an email</span>
                                </a>
                                <a href="https://wa.me/8801234567890" 
                                   class="flex items-center p-3 bg-white rounded-lg hover:shadow-md transition-shadow">
                                    <i class="fab fa-whatsapp text-success mr-3"></i>
                                    <span>WhatsApp chat</span>
                                </a>
                            </div>
                        </div>
                        
                        <!-- Social Media -->
                        <div class="bg-gray-50 rounded-xl p-6">
                            <h3 class="text-xl font-semibold mb-4">Follow Us</h3>
                            <div class="flex space-x-4">
                                <a href="#" class="bg-accent text-white w-10 h-10 rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors">
                                    <i class="fab fa-facebook"></i>
                                </a>
                                <a href="#" class="bg-accent text-white w-10 h-10 rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="#" class="bg-accent text-white w-10 h-10 rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors">
                                    <i class="fab fa-linkedin"></i>
                                </a>
                                <a href="#" class="bg-primary text-white w-10 h-10 rounded-full flex items-center justify-center hover:bg-secondary transition-colors">
                                    <i class="fab fa-github"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-primary mb-4">Find Us</h2>
                <p class="text-xl text-gray-600">Visit our office in the heart of Dhaka</p>
            </div>
            
            <!-- Map Placeholder -->
            <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                <div class="bg-gradient-to-br from-accent/20 to-success/20 h-96 flex items-center justify-center">
                    <div class="text-center">
                        <i class="fas fa-map-marker-alt text-4xl text-accent mb-4"></i>
                        <h3 class="text-xl font-semibold mb-2">GreenSoft BD Office</h3>
                        <p class="text-gray-600">123 Tech Street, Gulshan-2, Dhaka 1212</p>
                        <a href="https://maps.google.com" target="_blank" 
                           class="inline-block mt-4 bg-accent hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                            View on Google Maps
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-primary mb-4">Frequently Asked Questions</h2>
                <p class="text-xl text-gray-600">Common questions about our services and process</p>
            </div>
            
            <div class="max-w-3xl mx-auto space-y-6">
                <div class="bg-gray-50 rounded-xl p-6">
                    <h3 class="text-lg font-semibold mb-3">How long does a typical project take?</h3>
                    <p class="text-gray-600">Project timelines vary based on complexity and scope. Simple projects may take 2-4 weeks, while complex enterprise solutions can take 3-6 months. We provide detailed timelines during our initial consultation.</p>
                </div>
                
                <div class="bg-gray-50 rounded-xl p-6">
                    <h3 class="text-lg font-semibold mb-3">Do you provide ongoing support and maintenance?</h3>
                    <p class="text-gray-600">Yes, we offer comprehensive support and maintenance packages to ensure your software continues to perform optimally. This includes bug fixes, security updates, and feature enhancements.</p>
                </div>
                
                <div class="bg-gray-50 rounded-xl p-6">
                    <h3 class="text-lg font-semibold mb-3">What technologies do you specialize in?</h3>
                    <p class="text-gray-600">We specialize in modern web technologies including React, Node.js, Python, and cloud platforms like AWS. We also have extensive experience with Shopify app development and SaaS platforms.</p>
                </div>
                
                <div class="bg-gray-50 rounded-xl p-6">
                    <h3 class="text-lg font-semibold mb-3">Can you work with our existing team?</h3>
                    <p class="text-gray-600">Absolutely! We often collaborate with in-house teams and can integrate seamlessly into your existing development workflow. We're flexible and adapt to your preferred communication and project management tools.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-primary text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-code text-accent text-2xl mr-2"></i>
                        <span class="text-2xl font-bold">GreenSoft BD</span>
                    </div>
                    <p class="text-gray-300 mb-6 max-w-md">
                        Building innovative software solutions that drive business growth and digital transformation.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-300 hover:text-accent transition-colors">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-accent transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-accent transition-colors">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-accent transition-colors">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="about.html" class="text-gray-300 hover:text-accent transition-colors">About Us</a></li>
                        <li><a href="services.html" class="text-gray-300 hover:text-accent transition-colors">Services</a></li>
                        <li><a href="projects.html" class="text-gray-300 hover:text-accent transition-colors">Projects</a></li>
                        <li><a href="partners.html" class="text-gray-300 hover:text-accent transition-colors">Partners</a></li>
                        <li><a href="contact.html" class="text-gray-300 hover:text-accent transition-colors">Contact</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Info</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-2"></i>
                            <EMAIL>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone mr-2"></i>
                            +880 1234 567890
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            Dhaka, Bangladesh
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-12 pt-8 text-center text-gray-300">
                <p>&copy; 2024 GreenSoft BD. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script>
        // Mobile Menu Toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Contact Form Handling
        document.getElementById('contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            // Simple validation
            if (!data.firstName || !data.lastName || !data.email || !data.message) {
                alert('Please fill in all required fields.');
                return;
            }
            
            // Simulate form submission
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;
            
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
            submitButton.disabled = true;
            
            setTimeout(() => {
                alert('Thank you for your message! We\'ll get back to you within 24 hours.');
                this.reset();
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }, 2000);
        });
    </script>
</body>
</html>
